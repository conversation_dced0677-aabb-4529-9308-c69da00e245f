from allm_helper.llm_begin_helper import chat_helper_factory
import pytest
import os
import tempfile
import time

@pytest.fixture
def client(chat_helper_factory):
    aistudio_agent = chat_helper_factory(
        platform_name="aistudio",
        user_email="<EMAIL>",
        conversation_params={
            "system_prompt": "你是一个有用的助手",
            "model_name": "Gemini 2.5 Flash"
        }
    )
    return aistudio_agent

def test_base(client):
    # 测试简单聊天
    response = client.chat("1+1=?")
    assert response is not None
    assert len(response.strip()) > 0
    assert "2" in response

def test_file(client):
    # 测试文件上传
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
        temp_file.write("这是一份临时文件")
        temp_file_path = temp_file.name
    response = client.chat("请描述一下这个文件的内容。", attachments=[temp_file_path])
    assert response is not None
    assert len(response.strip()) > 0
    os.remove(temp_file_path)

